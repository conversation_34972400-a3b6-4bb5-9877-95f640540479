import Payroll from "../../modules/Settings/components/Payroll/Payroll";

export type PayrollTemplate = {
  name: string;
  country: string;
  job_titles: string[];
  effective_date: string;
  effective_from: string;
  effective_to: string;
  template_name: string;
  components: PayrollComponent[];
};

export type PayrollTemplateV2 = {
  name: string;
  country: string;
  job_titles: string[];
  effective_date: string;
  effective_from: string;
  effective_to: string;
  template_name: string;
  description: string | null;
  active: boolean;
  employee_types: string;
  components: PayrollComponentV2[];
  active: boolean;
};

export type EmployeeAdminConfig = Pick<PayrollTemplate, "components" | "effective_date" | "template_name">;

type PayrollComponent = {
  id: string;
  name: string;
  currency: string;
  amount: number;
  sub_components: PayrollComponent[];
};

type CompensationComponent = {
  id: string;
  name: string;
  code: string;
  currency: string;
  formula: Formula;
  mandatory: boolean;
  taxable: boolean;
  system_defined: boolean;
  pro_rated: boolean;
  pay_type: string;
  calculation_type: string;
  component_type: string;
  sort_order: null;
};

type PayrollComponentV2 = {
  compensation_component: CompensationComponent;
  amount: number;
};

enum CalculationTypes {
  Flat = "Flat",
  Percentage = "Percentage",
  Formula = "Formula",
  SystemDefined = "SystemDefined",
}

export type Formulae = {
  value: string;
  code: string | null;
  calculation_type: CalculationTypes;
  display_name: string;
};

export type CreatePayrollComponent = Omit<PayrollTemplate, "components"> & {
  components: PayrollTemplateComponent;
};
export type PayrollTemplateComponent = {
  id: string;
  name: string;
  sub_components: PayrollTemplateComponent[];
};

export type CompensationTemplateDetailRequest = {
  business_unit: string;
  department: string;
  work_role: string;
  job_title: string;
  employee_type: string;
  country: string;
};
