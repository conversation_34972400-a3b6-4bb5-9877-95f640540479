import { Box, Container, Typography, useTheme } from "@mui/material";
import { MRT_ColumnDef } from "material-react-table";
import React, { useMemo } from "react";
import DataTable from "src/modules/Common/Table/DataTable";
import { PayrollComponentV2 } from "src/services/api_definitions/payroll.service";

type Props = {
  compensation: PayrollComponentV2[];
  currency: string;
  locale?: string;
};

//Todo: Remove this any type and add proper types
export const formatCurrency = (amount: number, currency: string, locale: string = "IN") => {
  // this null undefined check is to show 0's if present in comp
  if (amount === null || amount === undefined || !locale) {
    return "";
  }
  return new Intl.NumberFormat(`en-${locale}`, { style: "currency", currency }).format(amount);
};

export const calculateTotalCTC = (data: PayrollComponentV2[] = []): number =>
  data.reduce((total, item) => total + (item?.amount || 0), 0);

const EmployeeCompensation: React.FC<Props> = ({ compensation, locale = "IN", currency }) => {
  console.log({ compensation });

  const theme = useTheme();
  const compensationCurrency = currency || "INR";
  const totalCTC = useMemo(() => calculateTotalCTC(compensation || []), [compensation]);

  const columns = useMemo<MRT_ColumnDef<any>[]>(
    () => [
      {
        accessorKey: "compensation_component.name",
        header: "Component",
        size: 300,
      },
      {
        header: "Monthly",
        size: 150,
        Cell: ({ row }) => {
          return formatCurrency(row.original.amount / 12, currency, locale);
        },
      },
      {
        accessorKey: "amount",
        header: "Annually",
        size: 150,
        accessorFn: (row) => formatCurrency(row.amount, currency, locale),
      },
    ],
    [],
  );

  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          mb: 1,
          borderRadius: 4,
          p: 2,
          bgcolor: theme.palette.primary.main,
          color: theme.palette.primary.contrastText,
        }}
      >
        <Typography variant="h4">{`CTC: ${formatCurrency(totalCTC, compensationCurrency, locale)}`}</Typography>
      </Box>
      <DataTable
        columns={columns}
        data={compensation || []}
        state={{
          expanded: true,
        }}
        enableStickyHeader
        muiTableContainerProps={{
          sx: {
            maxHeight: "100%",
            height: "100%",
          },
        }}
      />
    </Container>
  );
};

export default EmployeeCompensation;
