import { Input as BaseInput } from "@mui/base/Input";
import { Box, styled } from "@mui/system";
import * as React from "react";

export default function GroupedInput({
  segmentLengths,
  value,
  onChange,
  separator = <div style={{ width: "10px", alignSelf: "center", color: "gray" }}>/</div>,
}: {
  segmentLengths: number[];
  value: string;
  onChange: (val: string) => void;
  separator?: React.ReactNode;
}) {
  const inputRefs = React.useRef<HTMLInputElement[]>([]);
  const totalLength = segmentLengths.reduce((sum, len) => sum + len, 0);

  const getSegmentStartIndex = (segmentIndex: number) =>
    segmentLengths.slice(0, segmentIndex).reduce((a, b) => a + b, 0);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, segmentIndex: number) => {
    const segmentStart = getSegmentStartIndex(segmentIndex);
    const segmentLength = segmentLengths[segmentIndex];
    const newValue = e.target.value.slice(0, segmentLength);

    const currentValueArray = value.split("");
    for (let i = 0; i < segmentLength; i++) {
      currentValueArray[segmentStart + i] = newValue[i] ?? "";
    }

    const newFullValue = currentValueArray.join("").slice(0, totalLength);
    onChange(newFullValue);

    if (newValue.length === segmentLength && segmentIndex < segmentLengths.length - 1) {
      inputRefs.current[segmentIndex + 1]?.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, segmentIndex: number) => {
    if (e.key === "ArrowLeft" && segmentIndex > 0) {
      e.preventDefault();
      inputRefs.current[segmentIndex - 1]?.focus();
    }
    if (e.key === "ArrowRight" && segmentIndex < segmentLengths.length - 1) {
      e.preventDefault();
      inputRefs.current[segmentIndex + 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>, segmentIndex: number) => {
    e.preventDefault();
    const text = e.clipboardData.getData("text/plain").replace(/\s/g, "").slice(0, totalLength);
    const newValArray = value.split("");
    const start = getSegmentStartIndex(segmentIndex);

    for (let i = 0; i < text.length; i++) {
      if (start + i < totalLength) newValArray[start + i] = text[i];
    }

    const pastedValue = newValArray.join("").slice(0, totalLength);
    onChange(pastedValue);
  };

  return (
    <Box sx={{ display: "flex", gap: 1 }}>
      {segmentLengths.map((segLength, i) => {
        const start = getSegmentStartIndex(i);
        const segmentValue = value.slice(start, start + segLength);
        return (
          <React.Fragment key={i}>
            <BaseInput
              slots={{ input: InputElement }}
              slotProps={{
                input: {
                  ref: (el: HTMLInputElement) => (inputRefs.current[i] = el),
                  value: segmentValue,
                  onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleChange(e, i),
                  onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => handleKeyDown(e, i),
                  onPaste: (e: React.ClipboardEvent<HTMLInputElement>) => handlePaste(e, i),
                },
              }}
            />
            {i !== segmentLengths.length - 1 && separator}
          </React.Fragment>
        );
      })}
    </Box>
  );
}

const InputElement = styled("input")(({ theme }) => ({
  width: 100,
  height: 40,
  padding: "8px",
  fontSize: "1rem",
  textAlign: "center",
  borderRadius: 8,
  border: `1px solid ${theme.palette.mode === "dark" ? "#555" : "#ccc"}`,
  background: theme.palette.mode === "dark" ? "#222" : "#fff",
  color: theme.palette.mode === "dark" ? "#eee" : "#000",
}));
