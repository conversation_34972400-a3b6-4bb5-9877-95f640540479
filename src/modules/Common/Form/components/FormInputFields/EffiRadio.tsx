import { Box, FormControlLabel, Radio, RadioGroup } from "@mui/material";
import React from "react";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import { useFieldContext } from "../../effiFormContext";

type EffiCheckboxProps = {
  label: string;
  required?: boolean;
  options: {
    value: string;
    label: string;
  }[];
};

const EffiCheckbox: React.FC<EffiCheckboxProps> = ({ label, options }) => {
  const field = useFieldContext();
  console.log("field", field, field.state);
  return (
    <Box display="flex" flexDirection="column" sx={{ marginLeft: "11px" }}>
      <FormControlLabel
        label={label}
        // required={required}
        control={
          <RadioGroup
            row
            name={field.name}
            value={field.state.value}
            onChange={(ev) => {
              const raw = ev.target.value;
              const parsed = raw === "true" ? true : raw === "false" ? false : raw;
              return field.handleChange(parsed);
            }}
            id={field.name}
          >
            {options?.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={<CustomInputLabel labelProps={{ marginBottom: "0" }} title={option.label} />}
              />
            ))}
          </RadioGroup>
        }
      />
    </Box>
  );
};

export default EffiCheckbox;
