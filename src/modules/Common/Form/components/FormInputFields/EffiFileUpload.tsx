import { AttachFileOutlined, DeleteOutlined, FileDownloadOutlined } from "@mui/icons-material";
import { Box } from "@mui/material";
import React from "react";
import { useFieldContext } from "src/modules/Common/Form/effiFormContext";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import fileuploaderService from "src/services/fileuploader.service";
import FileDropzone from "../../../FileDropzone/FileDropzone";
import { CustomDropzoneContainer } from "../../../FileDropzone/style";
import Span from "../../../Span/Span";

interface EffiFileUploadProps {
  label?: string;
  required?: boolean;
  acceptFileTypes?: Record<string, string[]>;
  height?: number | string;
  value?: string | null;
  onChange?: (value: any) => void;
  disabled?: boolean;
  variant?: "default" | "success" | "error" | "warning" | "disabled";
}

const EffiFileUpload: React.FC<EffiFileUploadProps> = ({
  label,
  required,
  acceptFileTypes,
  height = 200,
  disabled = false,
  variant = "default",
}) => {
  const field = useFieldContext();

  const downloadFile = async () => {
    if (!field.state.value) return;
    await fileuploaderService.downloadDocumentS3(field.state.value as string);
  };

  if (field.state.value) {
    return (
      <Box sx={{ mt: 2, position: "relative" }}>
        <CustomInputLabel title={label} required={required} />
        {!disabled && (
          <DeleteOutlined
            onClick={() => field.handleChange(null)}
            sx={{
              flex: 1,
              margin: "4px",
              fill: "#667085",
              cursor: "pointer",
              alignSelf: "flex-end",
              position: "absolute",
              right: 0,
            }}
          />
        )}
        <CustomDropzoneContainer
          sx={{
            height,
            width: "100%",
            borderRadius: "10px",
            border: "1px dashed #007F6F",
            backgroundColor: "#A1CBC7",
            flexDirection: "column",
            cursor: "pointer",
          }}
          onClick={downloadFile}
        >
          <FileDownloadOutlined sx={{ width: 50, height: 50 }} />
          <Box display="flex" alignItems="center">
            <AttachFileOutlined sx={{ width: 16, height: 16 }} />
            &nbsp;
            <Span sx={{ fontSize: "13px" }}>{label}</Span>
          </Box>
        </CustomDropzoneContainer>
      </Box>
    );
  }

  return (
    <>
      <CustomInputLabel title={label} required={required} />
      <FileDropzone
        variant={variant}
        height={height}
        width="100%"
        acceptFileTypes={acceptFileTypes}
        onFileDrop={(files) => {
          if (files.length > 0) {
            field.handleChange(files[0]);
          }
        }}
        disabled={disabled}
      />
    </>
  );
};

export default EffiFileUpload;
