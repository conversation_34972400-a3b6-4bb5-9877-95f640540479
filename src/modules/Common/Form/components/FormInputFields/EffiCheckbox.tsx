import { Box, Checkbox, FormControlLabel } from "@mui/material";
import React from "react";
import { useFieldContext } from "../../effiFormContext";

type EffiCheckboxProps = {
  label: string;
  required?: boolean;
};

const EffiCheckbox: React.FC<EffiCheckboxProps> = ({ label, required }) => {
  const field = useFieldContext();
  console.log("field", field, field.state);
  return (
    <Box display="flex" flexDirection="column">
      <FormControlLabel
        label={label}
        required={required}
        control={
          <Checkbox
            id={field.name}
            name={field.name}
            data-testId={field.name}
            checked={!!field.state.value}
            onChange={(_ev, checked) => field.handleChange(checked)}
          />
        }
      />
    </Box>
  );
};

export default EffiCheckbox;
