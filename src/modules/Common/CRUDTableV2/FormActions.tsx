import { Button, DialogActions } from "@mui/material";
import React from "react";
import ButtonWithLoading from "../ButtonWithLoading/ButtonWithLoading";

interface FormActionsProps {
  onSubmitClick: () => void;
  cancelButtonText?: string;
  submitButtonText?: string;
  onCancelClick: () => void;
  disabled?: boolean | any;
  hideCancelButton?: boolean;
  isLoading?: boolean;
}

export const FormActions = ({
  onSubmitClick,
  cancelButtonText,
  submitButtonText,
  onCancelClick,
  disabled,
  hideCancelButton,
  isLoading,
}: FormActionsProps) => {
  return (
    <DialogActions sx={{ margin: "16px" }}>
      {!hideCancelButton && (
        <Button variant="outlined" onClick={onCancelClick}>
          {cancelButtonText || "Cancel"}
        </Button>
      )}
      <ButtonWithLoading variant="contained" onClick={onSubmitClick} disabled={disabled} isLoading={isLoading}>
        {submitButtonText || "Submit"}
      </ButtonWithLoading>
    </DialogActions>
  );
};
