import React, { useEffect, useImperativeHandle } from "react";

import { Box, Button, Grid } from "@mui/material";
import { useForm } from "src/customHooks/useForm";
import Span from "src/modules/Common/Span/Span";

import { Add } from "@mui/icons-material";
import { nanoid } from "@reduxjs/toolkit";
import { BaseObject } from "src/app/global";
import validators from "src/utils/validators";
import LoadingScreen from "../LoadingScreen";
import { FormDataType, FormDataTypeDocument, FormInputType, StepperComponentProps } from "../types/FormDataTypes";
import { getEnumValues, uploadEmployeeDocument } from "../utils/utils";
import { CommonForm } from "./CommonForm";

const acceptFileTypes = {
  "application/zip": [".zip"],
  "application/pdf": [".pdf"],
  "image/jpeg": [".jpg", ".jpeg"],
  "image/png": [".png"],
  "application/msword": [".doc"],
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"],
};

const addButtonStyle = {
  margin: 0,
  fontWeight: 500,
  fontSize: "16px",
  marginTop: "8px",
  textTransform: "none",
  fontFamily: "Poppins",
};

type Props = StepperComponentProps & {
  formData?: FormDataType[];
  disbaleFields?: string[];
  onFormComplete: (form: FormDataType, isFormSubmit?: boolean, isSaveDraft?: boolean) => void;
  disabledInputFields?: string[];
  myDocuments?: boolean;
  allowedMyDocuments?: string[];
  onFormChange?: (form: BaseObject) => void;
};

const isDocumentTypeAllowed = (documentType: string) => {
  return (
    documentType !== "Educational" &&
    documentType !== "Offer Letter" &&
    documentType !== "Experience Letter" &&
    documentType !== "Canceled Cheque"
  );
};

const getDocmentTypes = (documentTypes: string) => {
  const { data, isLoading } = getEnumValues(documentTypes);
  return {
    data: isLoading ? [] : data?.filter((documentType) => isDocumentTypeAllowed(documentType as string)),
    isLoading,
  };
};

const emptyFormData: FormDataType[] = [];

const getIsRequired = (documentType: string) => {
  return documentType === "PAN" || documentType === "Aadhaar";
};

const DocumentForm = ({
  formData = emptyFormData,
  onFormComplete,
  formActionButton,
  setDisableNext,
  isViewOnlyMode,
  disabledInputFields,
  myDocuments = false,
  allowedMyDocuments = [],
  onFormChange,
}: Props) => {
  const { data: allowedDocumentTypes = [], isLoading: isDocumentLoading } = getDocmentTypes("DocumentType");
  const documentTypes = myDocuments ? allowedMyDocuments : allowedDocumentTypes;
  const [form, setForm] = React.useState<FormInputType[]>([]);

  useEffect(() => {
    if (!isDocumentLoading && documentTypes) {
      const uploadedDocuments = formData[0] ? Object.values(formData[0] as FormDataTypeDocument).filter((f) => f) : [];
      const uploadedDocumentsType = uploadedDocuments.map(({ document_type }) => document_type);
      const restDocumentTypes = documentTypes.filter(
        (documentType) => !uploadedDocumentsType.includes(documentType as string),
      );

      const existingDocumentForm: FormInputType[] = formData[0]
        ? Object.entries(formData[0] as FormDataTypeDocument)
            .filter(([_, f]) => f)
            .map(([key, { document_type }]) => ({
              name: key,
              label: document_type,
              variant: "file",
              documentType: document_type,
              acceptFileTypes,
              isRequired: getIsRequired(document_type),
              height: 150,
            }))
        : [];
      const filteredExistingDocumentForm = existingDocumentForm.filter((document) =>
        isDocumentTypeAllowed(document.documentType as string),
      );

      const restDocumentForm: FormInputType[] = restDocumentTypes.map((documentType) => ({
        name: nanoid(),
        label: documentType,
        variant: "file",
        documentType,
        acceptFileTypes,
        isRequired: getIsRequired(documentType as string),
        height: 150,
      }));

      setForm([...filteredExistingDocumentForm, ...restDocumentForm]);
    }
  }, [isDocumentLoading, formData]);

  const getPanAdhaarValidations = (form: FormInputType[]) => {
    const panAdhaarKeys = form.filter((formDetail) => formDetail.label === "PAN" || formDetail.label === "Aadhaar");
    return {
      [panAdhaarKeys?.[0]?.name]: [validators.validateInput],
      [panAdhaarKeys?.[1]?.name]: [validators.validateInput],
    };
  };

  const getPanAdhaarInitialFormDetails = (form: FormInputType[]) => {
    const panAdhaarKeys = form.filter((formDetail) => formDetail.label === "PAN" || formDetail.label === "Aadhaar");
    const previousFormData = formData[0] ? formData[0] : {};
    const previousPanKey =
      Object.keys(previousFormData).find((key) => previousFormData[key]?.document_type === "PAN") || "";
    const previousAadhaarKey =
      Object.keys(previousFormData).find((key) => previousFormData[key]?.document_type === "Aadhaar") || "";
    const panData = previousFormData[previousPanKey] || "";
    const aadhaarData = previousFormData[previousAadhaarKey] || "";
    return {
      ...previousFormData,
      [panAdhaarKeys?.[0]?.name]: panData,
      [panAdhaarKeys?.[1]?.name]: aadhaarData,
    };
  };
  const initialFormDetails = React.useMemo(() => getPanAdhaarInitialFormDetails(form), [form?.length]);

  const { formDetails, formErrors, setFormDetail, areFormDetailsValid } = useForm({
    initialState: initialFormDetails,
    validations: getPanAdhaarValidations(form),
  });

  useEffect(() => {
    onFormChange?.(formDetails as BaseObject);
  }, [formDetails]);

  useEffect(() => {
    setDisableNext?.(!areFormDetailsValid);
  }, [areFormDetailsValid]);

  useImperativeHandle(formActionButton, () => ({
    next: (isFormSubmit, isSaveDraft) => {
      onFormComplete(formDetails as FormDataType, isFormSubmit, isSaveDraft);
    },
  }));

  const onFileChange = async (fieldName: string, acceptedFiles: File[]) => {
    const documentType = form.find((field) => field.name === fieldName)?.documentType as string;
    const document = await uploadEmployeeDocument(acceptedFiles, documentType, myDocuments);
    setFormDetail(fieldName, document);
  };

  const onChange = (fieldName: string, value: unknown) => {
    onFileChange(fieldName, value as File[]);
  };

  const onAddOtherDocument = () => {
    setForm((prevForm) => [
      ...prevForm,
      {
        name: nanoid(),
        label: "Other",
        variant: "file",
        documentType: "Other",
        acceptFileTypes,
        isRequired: false,
        height: 150,
      },
    ]);
  };

  if (isDocumentLoading) return <LoadingScreen />;

  // Filter out the Educational document type
  const filteredFormDetails = Object.fromEntries(
    Object.entries(formDetails).filter(([_, value]) =>
      typeof value === "object" && value !== null ? isDocumentTypeAllowed(value.document_type as string) : true,
    ),
  );

  const isAnyDocumentUploaded = Object.values(filteredFormDetails).some((value) => value);

  if (isViewOnlyMode && !isAnyDocumentUploaded) {
    return (
      <Box sx={{ display: "flex", flexDirection: "column", width: "100%" }}>
        <Span sx={{ width: "100%", textAlign: "center" }}> No Documents uploaded</Span>
      </Box>
    );
  }

  const disabledInputFieldsObject = disabledInputFields?.reduce(
    (acc, field) => {
      return { ...acc, [field]: true };
    },
    {} as Record<string, boolean>,
  );

  return (
    <>
      <CommonForm
        onChange={onChange}
        inputElements={form}
        isViewOnlyMode={isViewOnlyMode}
        formErrors={formErrors as Record<string, string>}
        formValues={formDetails as Record<string, unknown>}
        disabledInputFields={disabledInputFieldsObject}
      />
      <Grid item>
        {!myDocuments && !isViewOnlyMode && (
          <Button variant="text" sx={addButtonStyle} onClick={onAddOtherDocument} startIcon={<Add fontSize="small" />}>
            Add Other documents
          </Button>
        )}
      </Grid>
    </>
  );
};

export default DocumentForm;
