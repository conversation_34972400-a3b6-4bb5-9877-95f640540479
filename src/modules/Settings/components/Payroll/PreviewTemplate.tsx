import { CalculateRounded } from "@mui/icons-material";
import { Box, Grid2, IconButton, Typography } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useEffect, useMemo } from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import DataTable from "src/modules/Common/Table/DataTable";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import { CompensationComponent } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { z } from "zod";

const previewSchema = z.object({
  preview_type: z.enum(["Gross", "CTC"]),
  preview_value: z.number().min(1, "Value is required"),
});

const PreviewTemplate: React.FC<{
  componentsToPreview: CompensationComponent[];
}> = ({ componentsToPreview }) => {
  const [previewData, setPreviewData] = React.useState<Record<string, any>>({});
  const previewMutation = useMutation({
    mutationKey: ["preview-template"],
    mutationFn: async (payload: any) => {
      const preview = await payrollService.computePreview(payload.components, payload.preview_value);
      setPreviewData(preview?.components);
      return preview;
    },
  });

  const previewForm = useAppForm({
    defaultValues: {
      preview_type: "CTC",
      preview_value: 0,
    },
    validators: {
      onSubmit: previewSchema,
    },
    onSubmit: (params: any) => {
      console.log(params.value);
      previewMutation.mutate({
        components: componentsToPreview.map((eachComponent) => {
          return {
            ...eachComponent,
            formula: {
              ...eachComponent?.formula,
              value: eachComponent?.formula?.value ? Number(eachComponent?.formula?.value) : 0,
            },
          };
        }),
        preview_value: params.value.preview_value,
      });
    },
  });

  useEffect(() => {
    previewForm.setFieldValue("preview_value", 1000000);
    previewForm.handleSubmit();
  }, []);

  const processedData = useMemo<CompensationComponent[]>(() => {
    // Group data by component_type
    const groupedData = componentsToPreview.reduce(
      (acc, item) => {
        if (!acc[item.component_type]) {
          acc[item.component_type] = [];
        }
        acc[item.component_type].push(item);
        return acc;
      },
      {} as Record<string, CompensationComponent[]>,
    );

    const result: (CompensationComponent & { isSection?: boolean, monthly?: number; annually?: number })[] = [];

    // Process each component type and add section headers
    Object.entries(groupedData).forEach(([componentType, components]) => {
      // Add section header
      result.push({
        name: `${componentType}s`,
        isSection: true,
        component_type: componentType,
        description: "",
      });

      // Add components under this section
      components.forEach((component) => {
        result.push({
          ...component,
        });
      });
    });

    return result;
  }, [componentsToPreview]);

  return (
    <Box display={"flex"} flexDirection="column" gap={2} marginTop={4}>
      <ContentHeader
        title="Template Review"
      />
      <Grid2 container spacing={0.5}>
        <Grid2 size={4}>
          <previewForm.AppField name="preview_value">
            {(field: any) => <field.EffiCurrency label="" endHelperText="CTC" required />}
          </previewForm.AppField>
        </Grid2>
        <Grid2>
          <IconButton color="primary" onClick={previewForm.handleSubmit}>
            <CalculateRounded fontSize="large" />
          </IconButton>
        </Grid2>
      </Grid2>
      <DataTable
        columns={[
          {
            accessorKey: "name",
            header: "Component",
            Cell: ({ row }) => {
              if (row?.original?.isSection) {
                return (
                  <Typography fontWeight={600} fontSize={16}>
                    {row.original.name}
                  </Typography>
                );
              }
              return row.original.name;
            },
          },
          {
            accessorKey: "formula.value",
            header: "Calculation Type",
            Cell: ({ row }) => {
              const component = row.original;
              if (component?.formula?.calculation_type === "Percentage") {
                return `${component.formula.value}% of ${component?.formula?.display_name}`;
              }
              return component.formula?.calculation_type || component?.formula?.calculation_type;
            },
          },
          {
            accessorKey: "monthly_value",
            header: "Amount Monthly",
            Cell: ({ row }) => {
              const value = previewData?.[row.original?.name];

              if (row?.original?.isSection) {
                return null;
              }

              if (!value) {
                return 0;
              }
              return formatCurrency(value / 12, row?.original?.currency as string);
            },
          },
          {
            accessorKey: "annual_value",
            header: "Amount Annually",
            Cell: ({ row }) => {
              const value = previewData?.[row.original?.name];

              if (row?.original?.isSection) {
                return null;
              }

              if (!value) {
                return 0;
              }
              return formatCurrency(value, row?.original?.currency as string);
            },
          },
        ]}
        data={processedData as (CompensationComponent & { isSection?: boolean })[]}
        muiTableBodyRowProps={({ row }) => ({
          sx: {
            backgroundColor: row.original.isSection ? "#F8FFFE" : "inherit",
            ...(row.original.isSection && {
              borderStyle: "dashed",
            }),
          },
        })}
      />
    </Box>
  );
};

export default PreviewTemplate;
