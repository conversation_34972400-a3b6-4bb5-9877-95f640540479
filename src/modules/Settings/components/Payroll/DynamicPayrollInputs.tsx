import { CloseRounded } from "@mui/icons-material";
import { <PERSON><PERSON><PERSON>, <PERSON>rid<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Typo<PERSON> } from "@mui/material";
import { Box } from "@mui/system";
import React, { useMemo } from "react";
import { CompensationComponent } from "../../../../services/api_definitions/payroll.service";

type Props = {
  form: any;
  components: CompensationComponent[];
};

const DynamicPayrollInputs: React.FC<Props> = ({ form, components }) => {
  const getIndexForComponent = (componentName: string) => {
    return components.findIndex((eachComponent) => eachComponent?.name === componentName);
  };

  const getValueFieldWRTComponentFormulaType = (component: CompensationComponent, idx: number) => {
    switch (component?.formula.calculation_type) {
      case "Flat":
        return (
          <form.AppField name={`components[${idx}].formula.value`}>
            {(field: any) => <field.EffiCurrency label="" currency={component?.currency} endHelperText="Annual" />}
          </form.AppField>
        );
      case "Percentage":
        return (
          <form.AppField name={`components[${idx}].formula.value`}>
            {(field: any) => (
              <field.EffiPercentageField label="" endHelperText={`of ${component?.formula?.display_name}`} />
            )}
          </form.AppField>
        );
      case "Formula":
        return (
          <form.AppField name={`components[${idx}].formula.value`}>
            {(field: any) => <field.EffiCurrency label="" />}
          </form.AppField>
        );
      case "System Defined":
        return (
          <Grid2 container justifyContent="center">
            <Grid2 size={12}>
              <Typography variant="body1" sx={{ fontStyle: "italic", color: "text.secondary" }}>
                System Defined
              </Typography>
            </Grid2>
          </Grid2>
        );
      default:
        return null;
    }
  };

  const sectionsToDisplay = useMemo(() => {
    return [...new Set(components?.map((eachComponent) => eachComponent?.component_type))];
  }, [components]);

  return sectionsToDisplay?.map((_eachComponentType, typeIndex) => (
    <Box key={typeIndex} display="flex" flexDirection="column" gap={2}>
      <Typography variant="h6" fontWeight={600} fontSize={18}>
        {`${_eachComponentType}s`}
      </Typography>
      <Divider orientation="horizontal" />
      {components
        .filter((eachComponent) => eachComponent?.component_type === _eachComponentType)
        ?.map((_eachComponent, idx: number) => (
          <Box key={idx}>
            <form.Field name="components" key={`${typeIndex}-${idx}`} mode="array">
              {(_subField: any) => (
                <Grid2 container spacing={1} justifyContent="center" alignItems="center">
                  <Grid2 size={5} justifySelf="center">
                    <Typography
                      variant="body2"
                      sx={{
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        width: 250,
                      }}
                    >
                      {_eachComponent?.name}
                    </Typography>
                  </Grid2>
                  <Grid2 size={6}>
                    {getValueFieldWRTComponentFormulaType(_eachComponent, getIndexForComponent(_eachComponent?.name))}
                  </Grid2>
                  <Grid2 size={1} textAlign="center">
                    <IconButton
                      onClick={() => _subField.removeValue(getIndexForComponent(_eachComponent?.name))}
                      sx={{ visibility: _eachComponent?.mandatory ? "hidden" : "visible" }}
                    >
                      <CloseRounded />
                    </IconButton>
                  </Grid2>
                </Grid2>
              )}
            </form.Field>
            <Divider key={`${typeIndex}-${idx}-divider`} />
          </Box>
        ))}
    </Box>
  ));
};

export default DynamicPayrollInputs;
