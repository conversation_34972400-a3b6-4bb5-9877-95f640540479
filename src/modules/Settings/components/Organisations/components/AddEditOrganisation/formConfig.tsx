import { OrganisationDetails } from "src/services/api_definitions/organisations.service";
import { z } from "zod";

const organisationNameForm = [
  {
    fieldProps: {
      name: "name",
    },
    formProps: {
      label: "Organisation Name",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 12,
    },
  },
];

const organisationProfileForm = [
  {
    fieldProps: {
      name: "hr_admin_name",
    },
    formProps: {
      label: "Primary Contact Name",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: "hr_admin_email",
    },
    formProps: {
      label: "Primary Contact Email",
      type: "email",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: "tax_deductor.tax_id",
    },
    formProps: {
      label: "Organisation PAN",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
];

const employeeSwitchForm = [
  {
    fieldProps: {
      name: "tax_deductor.is_employee",
    },
    formProps: {
      label: "",
      type: "radio",
      required: true,
      options: [
        {
          value: true,
          label: "Employee",
        },
        {
          value: false,
          label: "Non-Employee",
        },
      ],
    },
    containerProps: {
      size: 12,
    },
  },
];

const employeeTaxDeductorDetailsForm = (setSearchResponse: any) => [
  {
    fieldProps: {
      name: "tax_deductor.name",
      listeners: {
        onChange: (value: any) => {
          setSearchResponse(value?.value?.originalRow);
        },
      },
      getData: "value",
    },
    formProps: {
      label: "Name",
      type: "auto-complete",
      required: true,
      size: "small",
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: "tax_deductor.father_name",
    },
    formProps: {
      label: "Father's Name",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: "tax_deductor.contact_email",
    },
    formProps: {
      label: "Email",
      type: "text",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 4,
    },
  },
];

const nonEmployeeTaxDeductorDetailsForm = [
  {
    fieldProps: {
      name: "tax_deductor.display_name",
    },
    formProps: {
      label: "Name",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: "tax_deductor.father_name",
    },
    formProps: {
      label: "Father's Name",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: "tax_deductor.contact_email",
    },
    formProps: {
      label: "Email",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: "tax_deductor.designation",
    },
    formProps: {
      label: "Designation",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: "tax_deductor.contact_phone",
    },
    formProps: {
      label: "Phone Number",
      type: "phone",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
];

const addSeperatorAOCode = (input: string, segmentLengths: number[], separator = "/") => {
  let result = "";
  let currentIndex = 0;

  for (let i = 0; i < segmentLengths.length; i++) {
    const len = segmentLengths[i];
    const segment = input.slice(currentIndex, currentIndex + len);
    result += segment;

    if (i < segmentLengths.length - 1 && segment.length === len) {
      result += separator;
    }

    currentIndex += len;
  }

  return result;
};

const enrichAddressWithLatitudeLongitude = (addresses: any) => {
  return addresses.map((address: any) => {
    const isGeofenceEnabled = address?.geofence_enabled;
    return {
      ...address,
      latitude: !isGeofenceEnabled ? null : Number(address?.latitude),
      longitude: !isGeofenceEnabled ? null : Number(address?.longitude),
      geofence_radius_meters: !isGeofenceEnabled ? null : Number(address?.geofence_radius_meters),
      tax_profile: {
        ...address?.tax_profile,
        tax_id_type: "TAN",
        jurisdiction_name: addSeperatorAOCode(
          address?.tax_profile?.jurisdiction_name?.replaceAll("/", ""),
          [3, 2, 3, 2],
        ),
      },
    };
  });
};

const branchOfficeTanForm = (index: number) => [
  {
    fieldProps: {
      name: `addresses[${index}].tax_profile.tax_id`,
    },
    formProps: {
      label: "TAN",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].tax_profile.jurisdiction_name`,
    },
    formProps: {
      label: "TDS circle / AO code",
      type: "grouped-input",
      segmentLengths: [3, 2, 3, 2],
      required: true,
    },
    containerProps: {
      size: 8,
    },
  },
];

const branchAddressForm = (index: number, getDetailsfromZipcode: any) => [
  {
    fieldProps: {
      name: `addresses[${index}].address_line1`,
    },
    formProps: {
      label: "Address Line 1",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].address_line2`,
    },
    formProps: {
      label: "Address Line 2",
      type: "text",
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].zip_code`,
      listeners: {
        onChange: async (value: any) => {
          getDetailsfromZipcode(value?.value, index);
        },
      },
    },
    formProps: {
      label: "Zip code",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].city`,
    },
    formProps: {
      label: "City",
      type: "text",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].state`,
    },
    formProps: {
      label: "State",
      type: "text",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].country`,
    },
    formProps: {
      label: "Country",
      type: "text",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].geofence_enabled`,
    },
    formProps: {
      label: "Geofencing",
      type: "switch",
    },
    containerProps: {
      size: 12,
    },
  },
];

const branchGeoFenceForm = (index: number) => [
  {
    fieldProps: {
      name: `addresses[${index}].latitude`,
    },
    formProps: {
      label: "Latitude",
      type: "number",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].longitude`,
    },
    formProps: {
      label: "Longitude",
      type: "number",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].geofence_radius_meters`,
    },
    formProps: {
      label: "Geofencing Radius",
      type: "number",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
];

const getInitialValues = (selectedRow: OrganisationDetails | null) => {
  if (selectedRow) {
    const taxDeductor = selectedRow?.tax_deductor;
    const isEmployee = !!taxDeductor?.is_employee;
    console.log({
      ...selectedRow,
      tax_deductor: {
        ...taxDeductor,
        contact_phone: {
          countryCode: taxDeductor?.contact_phone?.country_code,
          phone: taxDeductor?.contact_phone?.number,
        },
        name: isEmployee ? { searchResult: taxDeductor?.name } : "",
        display_name: isEmployee ? "" : taxDeductor?.name,
        // jurisdiction_name: taxDeductor?.jurisdiction_name?.replaceAll("/", ""),
        is_employee: isEmployee,
      },
    });
    return {
      ...selectedRow,
      tax_deductor: {
        ...taxDeductor,
        contact_phone: {
          countryCode: taxDeductor?.contact_phone?.country_code,
          phone: taxDeductor?.contact_phone?.number,
        },
        name: isEmployee ? { searchResult: taxDeductor?.name } : "",
        display_name: isEmployee ? "" : taxDeductor?.name,
        // jurisdiction_name: taxDeductor?.jurisdiction_name?.replaceAll("/", ""),
        is_employee: isEmployee,
      },
    };
  }
  return {
    name: "",
    logo: "",
    hr_admin_email: "",
    hr_admin_name: "",
    tax_deductor: {
      is_employee: true,
      tax_id_type: "PAN", //
    },
    addresses: [
      {
        address_line1: "",
        address_line2: "",
        city: "",
        zip_code: "",
        state: "",
        country: "",
        geofence_enabled: false,
        head_office: true,
        // latitude: null,
        // longitude: null,
        // geofence_radius_meters: null,
        tax_profile: {
          tax_id: "",
          jurisdiction_name: "",
        },
      },
    ],
  };
};

const enrichPhoneNumber = (phone: any, isEmployee: boolean) => {
  console.log("phone", phone, isEmployee);
  if (isEmployee) {
    return null;
  }
  return phone?.countryCode + phone?.phone;
};

const addressSchema = z
  .object({
    address_line1: z.string().nonempty({
      message: "Address Line 1 is required",
    }),
    city: z.string().nonempty({
      message: "City is required",
    }),
    zip_code: z.string().nonempty({
      message: "Zip Code is required",
    }),
    country: z.string().nonempty({
      message: "Country is required",
    }),
    state: z.string().nonempty({
      message: "State is required",
    }),
    geofence_enabled: z.boolean(),
    latitude: z.number().nullable().optional(),
    longitude: z.number().nullable().optional(),
    geofence_radius_meters: z.number().nullable().optional(),
    tax_profile: z.object({
      tax_id: z.string().nonempty({
        message: "Tax ID is required",
      }),
      jurisdiction_name: z
        .string()
        .nonempty({
          message: "Jurisdiction Name is required",
        })
        .min(10, {
          message: "Jurisdiction Name must be at least 10 characters long",
        }),
    }),
  })
  .refine(
    (data) => {
      console.log("data", data);
      if (!data.geofence_enabled) return true;
      console.log("reached!! data");
      return (
        typeof data.latitude === "number" &&
        typeof data.longitude === "number" &&
        typeof data.geofence_radius_meters === "number"
      );
    },
    {
      message: "Latitude, Longitude, and Radius are required when geofence is enabled.",
      path: ["latitude"], // attaches error to `geofence_enabled`
    },
  );

const formSchema = z
  .object({
    name: z.string().nonempty({
      message: "Name is required",
    }),
    logo: z.string().nonempty({
      message: "Logo is required",
    }),
    hr_admin_email: z.string().nonempty({
      message: "Primary Contact Email is required",
    }),
    hr_admin_name: z.string().nonempty({
      message: "Primary Contact Name is required",
    }),
    tax_deductor: z.object({
      name: z
        .object({
          searchResult: z.string().optional(),
        })
        .optional()
        .or(z.string().optional()),
      display_name: z.string().optional(),
      father_name: z.string().nonempty({ message: "Father's Name is required" }),
      designation: z.string().optional(),
      tax_id: z.string().nonempty({ message: "Tax ID is required" }),
      is_employee: z.boolean().optional(),
    }),
    addresses: z.array(addressSchema),
  })
  .refine(
    (data) => {
      const isEmployee = !!data?.tax_deductor?.is_employee;
      return isEmployee
        ? !!data?.tax_deductor?.name
        : !!data?.tax_deductor?.display_name && !!data?.tax_deductor?.designation;
    },
    {
      message: "Name is required",
      // path: ["tax_deductor.name", "tax_deductor.display_name"],
    },
  );

export {
  enrichPhoneNumber,
  getInitialValues,
  branchAddressForm,
  branchGeoFenceForm,
  branchOfficeTanForm,
  enrichAddressWithLatitudeLongitude,
  addSeperatorAOCode,
  employeeTaxDeductorDetailsForm,
  nonEmployeeTaxDeductorDetailsForm,
  organisationNameForm,
  organisationProfileForm,
  employeeSwitchForm,
  formSchema,
};
