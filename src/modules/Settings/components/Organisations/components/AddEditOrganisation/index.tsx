import { Add, Delete } from "@mui/icons-material";
import { <PERSON>, <PERSON>ton, Card, DialogActions, Divider, IconButton, Typography } from "@mui/material";
import { nanoid } from "@reduxjs/toolkit";
import { useStore } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import { APIProvider, MapMouseEvent } from "@vis.gl/react-google-maps";
import React from "react";
import { globalEnvConfig } from "src/configs/global.config";
import EffiDynamicForm from "src/modules/Common/Form/components/EffiDynamicForm";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import { apiRegister } from "src/services";
import { OrganisationDetails } from "src/services/api_definitions/organisations.service";
import fileuploaderService from "src/services/fileuploader.service";
import locationService from "src/services/location.service";
import organisationsService from "src/services/organisations.service";
import GoogleMaps from "../GoogleMaps";
import {
  branchAddressForm,
  branchGeoFenceForm,
  branchOfficeTanForm,
  employeeSwitchForm,
  employeeTaxDeductorDetailsForm,
  enrichAddressWithLatitudeLongitude,
  enrichPhoneNumber,
  formSchema,
  getInitialValues,
  nonEmployeeTaxDeductorDetailsForm,
  organisationNameForm,
  organisationProfileForm,
} from "./formConfig";

type NewAddEditOrganisationModalProps = {
  isModalOpen: boolean;
  onClose: () => void;
  refetch: () => void;
  selectedRow: OrganisationDetails | null;
};

const NewAddEditOrganisationModal: React.FC<NewAddEditOrganisationModalProps> = ({
  isModalOpen,
  onClose,
  refetch,
  selectedRow,
}) => {
  const updateOrganisationMutation = useMutation({
    mutationKey: ["update-organisation"],
    mutationFn: async (request: any) => organisationsService.updateOrganisation(request),
    onSuccess: () => {
      onClose();
      refetch();
    },
  });

  const createOrganisationMutation = useMutation({
    mutationKey: ["create-organisation"],
    mutationFn: async (request: any) => {
      return organisationsService.createOrganisation(request);
    },
    onSuccess: () => {
      onClose();
      refetch();
    },
  });

  const fetchDetailsfromZipcode = useMutation({
    mutationKey: ["get-address-info"],
    mutationFn: (zipcode: string): Promise<any> => locationService.getAddressDetailsFromZipcode(zipcode),
  });

  const getDetailsfromZipcode = async (zipcode: string, index: number) => {
    if (zipcode.length !== 6) {
      return;
    }
    const resp = await fetchDetailsfromZipcode.mutateAsync(zipcode);
    form.setFieldValue(`addresses[${index}].city`, resp?.city);
    form.setFieldValue(`addresses[${index}].state`, resp?.state);
    form.setFieldValue(`addresses[${index}].country`, resp?.country);
    form.validate("change");
  };

  const onAddBranchClick = (field: any) => {
    const newBranch = {
      head_office: false,
      address_line1: "",
      address_line2: "",
      city: "",
      zip_code: "",
      state: "",
      country: "",
      geofence_enabled: false,
      display_address: "",
      latitude: null,
      longitude: null,
      geofence_radius_meters: null,
      tax_profile: {
        // tax_id_type: "PAN",
        tax_id: "",
        jurisdiction_name: "",
      },
    };

    field.pushValue(newBranch);
  };

  const form = useAppForm({
    defaultValues: getInitialValues(selectedRow),
    validators: {
      onChange: formSchema as any,
      onSubmit: formSchema as any,
    },
    onSubmit: ({ value }) => {
      if (selectedRow) {
        const isEmployee = !!value?.tax_deductor?.is_employee;
        const request = {
          hr_admin_email: selectedRow?.hr_admin_email as string,
          hr_admin_name: selectedRow?.hr_admin_name as string,
          logo: selectedRow?.logo as string,
          name: selectedRow?.name as string,
          new_name: value?.name,
          new_logo: value?.logo,
          new_hr_admin_email: value?.hr_admin_email as string,
          new_hr_admin_name: value?.hr_admin_name as string,
          new_addresses: enrichAddressWithLatitudeLongitude(value?.addresses),
          new_tax_deductor: {
            ...value?.tax_deductor,
            is_employee: isEmployee,
            tax_id_type: "PAN",
            name: isEmployee ? value?.tax_deductor?.name?.searchResult : value?.tax_deductor?.display_name,
            contact_phone: enrichPhoneNumber(value?.tax_deductor?.contact_phone, isEmployee),
          },
        };
        updateOrganisationMutation.mutate(request);
      } else {
        const isEmployee = !!value?.tax_deductor?.is_employee;
        const request = {
          ...value,
          tax_deductor: {
            ...value?.tax_deductor,
            tax_id_type: "PAN",
            name: isEmployee ? value?.tax_deductor?.name?.searchResult : value?.tax_deductor?.display_name,
            contact_phone: enrichPhoneNumber(value?.tax_deductor?.contact_phone, isEmployee),
          },
          addresses: enrichAddressWithLatitudeLongitude(value?.addresses),
        };
        createOrganisationMutation.mutate(request);
      }
    },
  });

  const id = nanoid();
  const {
    tax_deductor: { is_employee } = {},
    addresses,
  } = useStore(form.store, (state: any) => state.values);
  console.log({ is_employee, addresses }, "global");

  const uploadLogoAndGetPath = async (selectedFiles: File[]) => {
    console.log({ selectedFiles });
    if (!selectedFiles?.[0]?.name) {
      return null;
    }
    const [_, extenstion] = selectedFiles[0].name.split(".");
    const hostname = window.location.hostname.split(".")[0];
    const fileName = `${hostname}-logo-${id}-org.${extenstion}`;
    const formData = new FormData();
    formData.append("file", selectedFiles[0]);
    formData.append("key", fileName);

    const fileUploadResponse = await fileuploaderService.uploadFile(apiRegister.AWS_S3.paths["upload-logo"], formData);
    if (fileUploadResponse.type === "success") {
      return fileUploadResponse.message;
    }

    return null;
  };

  const onMapChange = (ev: MapMouseEvent["detail"]["latLng"], index: number) => {
    // this id is required to identify the row in the form.
    form.setFieldValue(`addresses[${index}].latitude`, ev?.lat);
    form.setFieldValue(`addresses[${index}].longitude`, ev?.lng);
    form.validate("change");
  };

  const setSearchResponse = (originalRow: any) => {
    if (originalRow) {
      // form.setFieldValue("tax_deductor.name", {searchResult: "Sami Alam", originalRow: originalRow});
      form.setFieldValue("tax_deductor.contact_email", originalRow?.email);
    }
  };

  const onLogoChange = async (value: any) => {
    const path = await uploadLogoAndGetPath([value?.value as File]);
    if (path) {
      form.setFieldValue("logo", path as string);
      form.validate("change");
    }
  };

  const logoInputFields = [
    {
      fieldProps: {
        name: "logo",
        listeners: {
          onChange: async (value: any) => {
            onLogoChange(value);
          },
        },
      },
      formProps: {
        type: "file-upload",
        label: "Organisation Logo",
        required: true,
        acceptFileTypes: {
          "image/jpeg": [".jpg", ".jpeg"],
          "image/png": [".png"],
          "image/svg+xml": [".svg"],
        },
        height: 130,
      },
      containerProps: {
        size: 12,
      },
    },
  ];

  const onSameAsHeadOfficeChange = (value: any, idx: number) => {
    const addresses = form.getFieldValue("addresses") || [];
    const headOfficeAddress = addresses.find((each: any) => each.head_office);
    console.log({ headOfficeAddress, value }, "headOfficeAddress above");
    if (headOfficeAddress && value?.value) {
      console.log("reached!! headOfficeAddress", headOfficeAddress?.tax_profile, idx);
      form.setFieldValue(`addresses[${idx}].tax_profile`, headOfficeAddress?.tax_profile);
      form.validate("change");
    }
  };

  const sameAsHeadOfficeInputFields = (idx: number) => [
    {
      fieldProps: {
        name: `addresses[${idx}].same_as_head_office`,
        listeners: {
          onChange: (value: any) => {
            onSameAsHeadOfficeChange(value, idx);
          },
        },
      },
      formProps: {
        label: "Same as head office",
        type: "checkbox",
        required: true,
      },
      containerProps: {
        size: 12,
      },
    },
  ];

  return (
    <Modal
      title={selectedRow ? "Edit Organisation" : "Add Organisation"}
      subtitle={selectedRow ? "Update your company details for compliance and records." : "Enter organisation details."}
      showBackButton
      isOpen={isModalOpen}
      onClose={onClose}
      fullWidth
      actions={
        <DialogActions sx={{ margin: 2 }}>
          <form.Subscribe
            selector={(state) => [
              state.canSubmit,
              state.isSubmitting,
              state.isPristine,
              state.errorMap,
              state.values.addresses,
              state.isDefaultValue,
            ]}
          >
            {([canSubmit, isSubmitting, isPristine, errorMap, address, isDefaultValue]) => {
              console.log({ canSubmit, isSubmitting, isPristine, errorMap, isDefaultValue, address });
              return (
                <Box>
                  <Button
                    sx={{ align: "right" }}
                    variant="contained"
                    disabled={
                      !canSubmit || (isSubmitting as boolean) || (isPristine as boolean) || (isDefaultValue as boolean)
                    }
                    onClick={form.handleSubmit}
                  >
                    Save
                  </Button>
                </Box>
              );
            }}
          </form.Subscribe>
        </DialogActions>
      }
    >
      <>
        <Card sx={{ padding: 2, marginBottom: 2, paddingTop: 1 }}>
          <Typography fontSize={16} fontWeight="600" mb={1}>
            Profile
          </Typography>
          <EffiDynamicForm form={form} inputFields={organisationNameForm} />
          <EffiDynamicForm formStyle={{ marginBottom: 2, marginTop: 2 }} form={form} inputFields={logoInputFields} />
          <Box display="flex" flexDirection="column" mb={2} mt={2}>
            <EffiDynamicForm form={form} inputFields={organisationProfileForm} />
          </Box>
          <Typography variant="body2" fontWeight="600" mb={2}>
            Tax Deductor Details
          </Typography>
          <EffiDynamicForm form={form} inputFields={employeeSwitchForm} />
          <EffiDynamicForm
            form={form}
            inputFields={
              is_employee ? employeeTaxDeductorDetailsForm(setSearchResponse) : nonEmployeeTaxDeductorDetailsForm
            }
          />
        </Card>
        <Card sx={{ padding: 2, marginBottom: 2 }}>
          <form.AppField name="addresses" mode="array">
            {(addressField: any) => (
              <>
                {addressField.state.value.map((address: any, idx: number) => (
                  <div key={address.id}>
                    <Typography fontSize={16} fontWeight="600" mb={2}>
                      {address.head_office ? "Head Office Location" : "Branch Location"}
                    </Typography>
                    <Typography variant="body2" fontWeight="600" mb={1}>
                      Tax Details
                    </Typography>
                    {!address.head_office && (
                      <EffiDynamicForm
                        form={form}
                        inputFields={sameAsHeadOfficeInputFields(idx)}
                        // formStyle={{ marginBottom: 2 }}
                      />
                    )}
                    <EffiDynamicForm
                      form={form}
                      inputFields={branchOfficeTanForm(idx)}
                      formStyle={{ marginBottom: 2 }}
                    />
                    <Typography variant="body2" fontWeight="600" mb={1}>
                      Address
                    </Typography>
                    <EffiDynamicForm
                      form={form}
                      inputFields={branchAddressForm(idx, getDetailsfromZipcode)}
                      formStyle={{ marginBottom: 1 }}
                    />
                    {address.geofence_enabled && (
                      <Box display="flex" flexDirection="column" gap={2}>
                        <APIProvider
                          apiKey={globalEnvConfig.GOOGLE_MAPS_API_KEY}
                          onLoad={() => console.log("Maps API has loaded.")}
                        >
                          <GoogleMaps
                            defaultCoords={{
                              lat: (address.latitude as number) || 0,
                              lng: (address.longitude as number) || 0,
                            }}
                            onChange={(ev) => onMapChange(ev, idx)}
                            radius={address.geofence_radius_meters as number}
                          />
                        </APIProvider>
                        <EffiDynamicForm
                          form={form}
                          inputFields={branchGeoFenceForm(idx)}
                          formStyle={{ marginTop: 2 }}
                        />
                      </Box>
                    )}
                    {!address.head_office && (
                      <Box display="flex" justifyContent="flex-end">
                        <IconButton onClick={() => addressField.removeValue(idx)}>
                          <Delete />
                        </IconButton>
                      </Box>
                    )}
                    {idx !== addresses.length - 1 && <Divider sx={{ marginBottom: 2 }} />}
                  </div>
                ))}
                <Button onClick={() => onAddBranchClick(addressField)} sx={{ marginTop: 2, minWidth: 100 }}>
                  <Add /> Add More
                </Button>
              </>
            )}
          </form.AppField>
        </Card>
      </>
    </Modal>
  );
};

export default NewAddEditOrganisationModal;
